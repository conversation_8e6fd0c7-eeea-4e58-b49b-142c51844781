# 🏥 Lung Cancer Detection System

Hệ thống phát hiện ung thư phổi hoàn chỉnh với AI tích hợp Survey Model, YOLO Detection và Gemini AI Analysis.

## 🎯 Tính năng chính

### 1. Survey Model (new_lung_cancer_model.pkl)
- **Type**: Ensemble (Random Forest + Gradient Boosting + Extra Trees + Neural Network)
- **Accuracy**: 99.78%
- **AUC Score**: 99.98%
- **Input**: 15 features từ khảo sát y tế
- **Features**: GENDER, AGE, SMOKING, YELLOW_FINGERS, ANXIETY, PEER_PRESSURE, CHRONIC DISEASE, FATIGUE, ALLERGY, WHEEZING, ALCOH<PERSON> CONSUMING, COUGHING, SHORTNESS OF BREATH, SWALLOWING DIFFICULTY, CHEST PAIN
- **Output**: YES/NO ung thư phổi với độ tin cậy

### 2. YOLOv11m Model (best.pt)
- **<PERSON><PERSON>t hiện**: 4 loại ung thư phổi
  - 🔴 Adenocarcinoma
  - 🟢 Small Cell Carcinoma
  - 🔵 Large Cell Carcinoma
  - 🟡 Squamous Cell Carcinoma
- **<PERSON><PERSON>n thị**: Bounding boxes với độ tin cậy
- **Input**: Ảnh CT scan (JPG, PNG, TIFF, etc.)

### 3. Gemini AI Analysis
- **Phân tích**: Kết hợp kết quả từ cả 2 models
- **Đánh giá**: Mức độ rủi ro tổng thể
- **Khuyến nghị**: Lời khuyên y tế chi tiết
- **Báo cáo**: Phân tích chuyên sâu bằng AI

### 4. Complete GUI System
- **4 Tabs chính**:
  - 📋 Survey Analysis: Khảo sát triệu chứng
  - 🖼️ Image Analysis: Phân tích ảnh CT
  - 🔬 Combined Analysis: Phân tích tổng hợp + AI
  - 📊 Results Summary: Tóm tắt và xuất báo cáo

## 🏗️ Kiến trúc hệ thống

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Complete GUI  │────│   FastAPI        │────│   AI Models     │
│   (Frontend)    │    │   Backend        │    │   + Gemini      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
        │                        │                        │
        │                        │                        │
   ┌─────────┐              ┌─────────┐              ┌─────────┐
   │ Survey  │              │ REST    │              │ Survey  │
   │ Image   │              │ API     │              │ YOLO    │
   │ Results │              │ CORS    │              │ Gemini  │
   └─────────┘              └─────────┘              └─────────┘
```

## 🚀 Cài đặt và sử dụng

### Bước 1: Cài đặt dependencies
```bash
# Cài đặt các package cần thiết
pip install -r requirements.txt
```

### Bước 2: Cấu hình Gemini API (Tùy chọn)
```bash
# Tạo file .env từ template
cp .env.example .env

# Chỉnh sửa .env và thêm API key
# GEMINI_API_KEY=your_actual_api_key_here
```

### Bước 3: Kiểm tra hệ thống
```bash
# Chạy test toàn bộ hệ thống
python test_system.py
```

### Bước 4: Khởi động hệ thống

#### Option 1: Sử dụng GUI hoàn chỉnh (Khuyến nghị)
```bash
# Terminal 1: Khởi động backend
python start_backend.py

# Terminal 2: Khởi động GUI
python complete_gui.py
```

#### Option 2: Sử dụng GUI đơn giản (Chỉ YOLO)
```bash
python simple_gui.py
```

## 📱 Hướng dẫn sử dụng GUI

### Tab 1: 📋 Survey Analysis
1. Điền đầy đủ thông tin khảo sát (15 câu hỏi)
2. Click "🔍 Analyze Survey"
3. Xem kết quả dự đoán và mức độ rủi ro

### Tab 2: 🖼️ Image Analysis
1. Click "📁 Select CT Scan Image"
2. Chọn file ảnh CT scan
3. Click "🔍 Analyze Image"
4. Xem kết quả phát hiện với bounding boxes

### Tab 3: 🔬 Combined Analysis
1. Hoàn thành Survey và Image analysis trước
2. Click "🚀 Run Complete Analysis"
3. Nhận phân tích AI chi tiết từ Gemini
4. Xem khuyến nghị y tế chuyên nghiệp

### Tab 4: 📊 Results Summary
1. Xem tóm tắt toàn bộ kết quả
2. Click "💾 Export Results" để lưu báo cáo

## 🔧 API Endpoints

### Backend API (http://localhost:8000)

- `GET /` - Health check
- `POST /predict/survey` - Survey prediction
- `POST /predict/image` - Image analysis
- `POST /analyze/combined` - Combined analysis with AI
- `GET /docs` - API documentation (Swagger UI)

### Ví dụ sử dụng API:

```python
import requests

# Survey prediction
survey_data = {
    'gender': 1, 'age': 45, 'smoking': 1,
    # ... other 12 features
}
response = requests.post('http://localhost:8000/predict/survey', data=survey_data)

# Image analysis
with open('ct_scan.jpg', 'rb') as f:
    files = {'file': f}
    response = requests.post('http://localhost:8000/predict/image', files=files)
```

## 🧪 Testing

### Chạy test tự động
```bash
python test_system.py
```

Test sẽ kiểm tra:
- ✅ Requirements và dependencies
- ✅ Model files tồn tại
- ✅ Backend API endpoints
- ✅ GUI components
- ✅ End-to-end functionality

## 📁 Cấu trúc dự án

```
lung-cancer-detection/
├── README.md                 # Documentation
├── requirements.txt          # Dependencies
├── .env.example             # Environment template
├── start_backend.py         # Backend launcher
├── test_system.py           # System tests
├── simple_gui.py            # Simple YOLO GUI
├── complete_gui.py          # Complete system GUI
├── backend/                 # Backend API
│   ├── main.py             # FastAPI application
│   ├── config.py           # Configuration
│   └── gemini_service.py   # Gemini AI service
└── model/                   # AI Models
    ├── best.pt             # YOLO model
    └── new_lung_cancer_model.pkl  # Survey model
```

## 🔬 Kết quả mẫu

### Survey Analysis
```
PREDICTION: YES
CONFIDENCE:
  • No Cancer: 15.2%
  • Cancer Risk: 84.8%
RISK LEVEL: HIGH
```

### Image Analysis
```
TOTAL DETECTIONS: 2
1. Adenocarcinoma (Confidence: 87.3%)
2. Small Cell Carcinoma (Confidence: 72.1%)
```

### AI Analysis
```
RISK ASSESSMENT: HIGH
RECOMMENDATIONS:
1. Khẩn cấp: Liên hệ bác sĩ chuyên khoa phổi ngay lập tức
2. Thực hiện các xét nghiệm bổ sung theo chỉ định
3. Chuẩn bị hồ sơ y tế đầy đủ cho buổi khám
```

## ⚙️ Cấu hình nâng cao

### Environment Variables (.env)
```bash
# Gemini API
GEMINI_API_KEY=your_gemini_api_key_here

# Backend
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8000
DEBUG=True

# Models
SURVEY_MODEL_PATH=model/new_lung_cancer_model.pkl
YOLO_MODEL_PATH=model/best.pt
YOLO_CONFIDENCE_THRESHOLD=0.25

# Logging
LOG_LEVEL=INFO
```

### Tùy chỉnh YOLO Detection
```python
# Trong backend/config.py
YOLO_CONFIDENCE_THRESHOLD = 0.25  # Ngưỡng tin cậy
CANCER_COLORS = {
    "Adenocarcinoma": (0, 0, 255),      # Đỏ
    "Small Cell Carcinoma": (0, 255, 0), # Xanh lá
    "Large Cell Carcinoma": (255, 0, 0), # Xanh dương
    "Squamous Cell Carcinoma": (0, 255, 255) # Vàng
}
```

## 🚨 Xử lý lỗi thường gặp

### 1. Backend không khởi động được
```bash
# Kiểm tra port 8000 có bị chiếm không
lsof -i :8000

# Thay đổi port trong .env
BACKEND_PORT=8001
```

### 2. Model không tải được
```bash
# Kiểm tra file models tồn tại
ls -la model/

# Kiểm tra quyền truy cập
chmod 644 model/*.pkl model/*.pt
```

### 3. Gemini API không hoạt động
```bash
# Kiểm tra API key trong .env
cat .env | grep GEMINI_API_KEY

# Test API key
curl -H "Authorization: Bearer $GEMINI_API_KEY" \
     https://generativelanguage.googleapis.com/v1/models
```

### 4. GUI không hiển thị ảnh
```bash
# Cài đặt lại Pillow
pip uninstall Pillow
pip install Pillow

# Kiểm tra tkinter
python -c "import tkinter; print('OK')"
```

## 📊 Performance

### System Requirements
- **RAM**: Tối thiểu 8GB (Khuyến nghị 16GB)
- **Storage**: 5GB trống
- **Python**: 3.8+
- **OS**: Windows 10+, macOS 10.15+, Ubuntu 18.04+

### Processing Time
- **Survey Analysis**: ~1-2 giây
- **Image Analysis**: ~5-15 giây (tùy kích thước ảnh)
- **Combined Analysis**: ~10-30 giây (bao gồm Gemini AI)

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📄 License

Dự án này được phát hành dưới MIT License. Xem file `LICENSE` để biết thêm chi tiết.

## ⚠️ Disclaimer

**QUAN TRỌNG**: Hệ thống này chỉ dành cho mục đích nghiên cứu và hỗ trợ sàng lọc. Không được sử dụng để thay thế chẩn đoán y tế chuyên nghiệp. Luôn tham khảo ý kiến bác sĩ chuyên khoa để có chẩn đoán và điều trị chính xác.

## 📞 Hỗ trợ

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 Documentation: [Wiki](https://github.com/your-repo/wiki)

---

**Phát triển bởi**: AI Medical Research Team
**Phiên bản**: 1.0.0
**Cập nhật lần cuối**: 2025-01-02
```
