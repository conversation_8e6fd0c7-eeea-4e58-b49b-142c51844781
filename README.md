# 🏥 Lung Cancer Detection GUI

Giao diện đơn giản để nhận diện ung thư phổi từ ảnh CT scan.

## 🎯 Tính năng


### 1. Survey Model (new_lung_cancer_model.pkl)
- **Type**: Ensemble (Random Forest + Gradient Boosting + Extra Trees + Neural Network)
- **Accuracy**: 99.78%
- **AUC Score**: 99.98%
- **Input**: 15 features từ khảo sát y tế
- **Output**: YES/NO ung thư phổi

### 2. YOLOv11m Model (best.pt)


- Nhận diện 4 loại ung thư phổi: Adenocarcinoma, Small Cell, Large Cell, Squamous Cell
- Giao diện GUI đơn giản với tkinter
- Hiển thị bounding boxes màu sắc
- <PERSON><PERSON><PERSON> gi<PERSON> mức độ tin cậy

## 🚀 Sử dụng

### Cài đặt
```bash
pip install -r requirements.txt
```

### Chạy GUI
```bash
python simple_gui.py
```

## � Cách dùng

1. Click "📁 Select CT Scan" → Chọn ảnh CT
2. Click "🔍 Analyze" → Xem kết quả
3. Quan sát bounding boxes:
   - 🔴 Adenocarcinoma
   - 🟢 Small Cell Carcinoma  
   - 🔵 Large Cell Carcinoma
   - 🟡 Squamous Cell Carcinoma

## ⚠️ Lưu ý

- Chỉ để nghiên cứu, không thay thế chẩn đoán y tế
- Cần file `best.pt` (model đã train)
