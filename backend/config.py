#!/usr/bin/env python3
"""
Configuration settings for Lung Cancer Detection Backend
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Application configuration"""

    # API Configuration
    HOST = os.getenv("BACKEND_HOST", "0.0.0.0")
    PORT = int(os.getenv("BACKEND_PORT", 8000))
    DEBUG = os.getenv("DEBUG", "True").lower() == "true"

    # Model Paths
    SURVEY_MODEL_PATH = os.getenv("SURVEY_MODEL_PATH", "../model/new_lung_cancer_model.pkl")
    YOLO_MODEL_PATH = os.getenv("YOLO_MODEL_PATH", "../model/best.pt")

    # Gemini API
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

    # Logging
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

    # YOLO Detection Settings
    YOLO_CONFIDENCE_THRESHOLD = float(os.getenv("YOLO_CONFIDENCE_THRESHOLD", 0.25))

    # Survey Model Features
    SURVEY_FEATURES = [
        'GENDER', 'AGE', 'SMOKING', 'YELLOW_FINGERS', 'ANXIETY',
        'PEER_PRESSURE', 'CHRONIC DISEASE', 'FATIGUE', 'ALLERGY',
        'WHEEZING', 'ALCOHOL CONSUMING', 'COUGHING',
        'SHORTNESS OF BREATH', 'SWALLOWING DIFFICULTY', 'CHEST PAIN'
    ]

    # Cancer Types
    CANCER_TYPES = {
        0: "Adenocarcinoma",
        1: "Large Cell Carcinoma",
        2: "Small Cell Carcinoma",
        3: "Squamous Cell Carcinoma"
    }

    # Colors for visualization (BGR format)
    CANCER_COLORS = {
        "Adenocarcinoma": (0, 0, 255),  # Red
        "Small Cell Carcinoma": (0, 255, 0),  # Green
        "Large Cell Carcinoma": (255, 0, 0),  # Blue
        "Squamous Cell Carcinoma": (0, 255, 255)  # Yellow
    }

# Create global config instance
config = Config()