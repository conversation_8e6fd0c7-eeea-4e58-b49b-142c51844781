#!/usr/bin/env python3
"""
Gemini AI Service for Lung Cancer Detection Analysis
Provides detailed medical analysis and recommendations
"""

import os
import logging
from typing import Dict, List, Optional, Any
import google.generativeai as genai
from datetime import datetime

logger = logging.getLogger(__name__)

class GeminiAnalysisService:
    """Service for Gemini AI medical analysis"""

    def __init__(self, api_key: Optional[str] = None):
        """Initialize Gemini service"""
        self.api_key = api_key or os.getenv("GEMINI_API_KEY")
        self.model = None
        self.is_configured = False

        if self.api_key and self.api_key != "your_gemini_api_key_here":
            try:
                genai.configure(api_key=self.api_key)
                self.model = genai.GenerativeModel('gemini-2.5-flash')
                self.is_configured = True
                logger.info("✅ Gemini AI service initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Gemini: {str(e)}")
        else:
            logger.warning("⚠️ Gemini API key not configured")

    async def analyze_combined_results(
        self,
        survey_result: Optional[Dict],
        image_result: Optional[Dict],
        patient_info: Dict
    ) -> Dict[str, Any]:
        """Analyze combined results from survey and image models"""

        if not self.is_configured:
            return self._get_fallback_analysis(survey_result, image_result)

        try:
            prompt = self._build_analysis_prompt(survey_result, image_result, patient_info)
            response = self.model.generate_content(prompt)

            if response.text:
                return self._parse_gemini_response(response.text, survey_result, image_result)
            else:
                logger.warning("Empty response from Gemini")
                return self._get_fallback_analysis(survey_result, image_result)

        except Exception as e:
            logger.error(f"Gemini analysis error: {str(e)}")
            return self._get_fallback_analysis(survey_result, image_result)

    def _build_analysis_prompt(
        self,
        survey_result: Optional[Dict],
        image_result: Optional[Dict],
        patient_info: Dict
    ) -> str:
        """Build comprehensive analysis prompt for Gemini"""

        prompt = """Bạn là một chuyên gia y tế AI chuyên về ung thư phổi. Hãy phân tích kết quả sau và đưa ra đánh giá chi tiết, chuyên nghiệp.

THÔNG TIN BỆNH NHÂN:"""

        # Patient information
        age = patient_info.get('age', 'N/A')
        gender = patient_info.get('gender', 'N/A')
        smoking = patient_info.get('smoking', 'N/A')

        prompt += f"""
- Tuổi: {age}
- Giới tính: {gender}
- Hút thuốc: {smoking}
"""

        # Survey results
        if survey_result:
            prompt += f"""
KẾT QUẢ KHẢO SÁT TRIỆU CHỨNG:
- Dự đoán ung thư phổi: {survey_result['prediction']}
- Độ tin cậy: {survey_result['confidence']['cancer']:.1%}
- Mức độ rủi ro: {survey_result['risk_level']}
"""
        else:
            prompt += "\nKẾT QUẢ KHẢO SÁT: Không có dữ liệu"

        # Image analysis results
        if image_result and image_result.get('detections'):
            prompt += f"""
KẾT QUẢ PHÂN TÍCH HÌNH ẢNH CT:
- Số vùng phát hiện: {image_result['total_detections']}
- Trạng thái: {image_result['status']}
- Chi tiết phát hiện:"""

            for i, detection in enumerate(image_result['detections']):
                prompt += f"""
  + Loại {i+1}: {detection['cancer_type']} (Độ tin cậy: {detection['confidence']:.1%})"""
        else:
            prompt += "\nKẾT QUẢ HÌNH ẢNH: Không phát hiện bất thường hoặc không có dữ liệu"

        prompt += """

Hãy đưa ra phân tích theo cấu trúc sau:

## ĐÁNH GIÁ TỔNG QUAN
[Đánh giá tình trạng tổng thể của bệnh nhân dựa trên các kết quả]

## PHÂN TÍCH CHI TIẾT
[Phân tích từng yếu tố rủi ro và kết quả kiểm tra]

## KHUYẾN NGHỊ
[Các khuyến nghị cụ thể cho bệnh nhân]

## CÁC BƯỚC TIẾP THEO
[Hướng dẫn các bước cần thực hiện tiếp theo]

## LƯU Ý QUAN TRỌNG
[Các lưu ý về giới hạn của công cụ và tầm quan trọng của chẩn đoán y tế chuyên nghiệp]

Hãy trả lời bằng tiếng Việt, chuyên nghiệp nhưng dễ hiểu."""

        return prompt

    def _parse_gemini_response(
        self,
        response_text: str,
        survey_result: Optional[Dict],
        image_result: Optional[Dict]
    ) -> Dict[str, Any]:
        """Parse and structure Gemini response"""

        # Extract sections from response
        sections = {
            "overview": "",
            "detailed_analysis": "",
            "recommendations": [],
            "next_steps": [],
            "important_notes": ""
        }

        try:
            # Simple parsing - split by sections
            current_section = None
            lines = response_text.split('\n')

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Detect section headers
                if "ĐÁNH GIÁ TỔNG QUAN" in line.upper():
                    current_section = "overview"
                elif "PHÂN TÍCH CHI TIẾT" in line.upper():
                    current_section = "detailed_analysis"
                elif "KHUYẾN NGHỊ" in line.upper():
                    current_section = "recommendations"
                elif "CÁC BƯỚC TIẾP THEO" in line.upper():
                    current_section = "next_steps"
                elif "LƯU Ý QUAN TRỌNG" in line.upper():
                    current_section = "important_notes"
                elif current_section:
                    # Add content to current section
                    if current_section in ["recommendations", "next_steps"]:
                        if line.startswith(('-', '+', '*', '•')) or line[0].isdigit():
                            sections[current_section].append(line)
                    else:
                        if sections[current_section]:
                            sections[current_section] += " " + line
                        else:
                            sections[current_section] = line

        except Exception as e:
            logger.error(f"Error parsing Gemini response: {str(e)}")

        # Determine overall risk assessment
        risk_assessment = self._determine_risk_level(survey_result, image_result)

        return {
            "analysis": response_text,
            "structured_analysis": sections,
            "recommendations": sections["recommendations"] if sections["recommendations"] else [
                "Tham khảo ý kiến bác sĩ chuyên khoa phổi",
                "Thực hiện các xét nghiệm bổ sung theo chỉ định",
                "Duy trì lối sống lành mạnh, không hút thuốc"
            ],
            "risk_assessment": risk_assessment,
            "confidence": "AI_GENERATED",
            "timestamp": datetime.now().isoformat()
        }

    def _determine_risk_level(
        self,
        survey_result: Optional[Dict],
        image_result: Optional[Dict]
    ) -> str:
        """Determine overall risk level based on results"""

        # High risk conditions
        if survey_result and survey_result.get('risk_level') == 'HIGH':
            return "HIGH"

        if image_result and image_result.get('total_detections', 0) > 0:
            return "HIGH"

        # Medium risk conditions
        if survey_result and survey_result.get('risk_level') == 'MEDIUM':
            return "MEDIUM"

        # Low risk
        if survey_result and survey_result.get('risk_level') == 'LOW':
            if not image_result or image_result.get('total_detections', 0) == 0:
                return "LOW"

        return "MEDIUM"  # Default to medium if unclear

    def _get_fallback_analysis(
        self,
        survey_result: Optional[Dict],
        image_result: Optional[Dict]
    ) -> Dict[str, Any]:
        """Provide fallback analysis when Gemini is not available"""

        # Determine risk level
        risk_level = self._determine_risk_level(survey_result, image_result)

        # Generate basic analysis
        analysis_parts = []

        if survey_result:
            if survey_result['prediction'] == 'YES':
                analysis_parts.append(f"Kết quả khảo sát cho thấy có dấu hiệu nghi ngờ ung thư phổi với độ tin cậy {survey_result['confidence']['cancer']:.1%}.")
            else:
                analysis_parts.append(f"Kết quả khảo sát không cho thấy dấu hiệu rõ ràng của ung thư phổi (độ tin cậy: {survey_result['confidence']['no_cancer']:.1%}).")

        if image_result:
            if image_result.get('total_detections', 0) > 0:
                analysis_parts.append(f"Phân tích hình ảnh CT phát hiện {image_result['total_detections']} vùng nghi ngờ.")
                for detection in image_result.get('detections', []):
                    analysis_parts.append(f"- {detection['cancer_type']}: {detection['confidence']:.1%}")
            else:
                analysis_parts.append("Phân tích hình ảnh CT không phát hiện bất thường rõ ràng.")

        analysis_text = " ".join(analysis_parts)

        # Generate recommendations based on risk level
        recommendations = []
        if risk_level == "HIGH":
            recommendations = [
                "Khẩn cấp: Liên hệ bác sĩ chuyên khoa phổi ngay lập tức",
                "Thực hiện các xét nghiệm bổ sung theo chỉ định của bác sĩ",
                "Chuẩn bị hồ sơ y tế đầy đủ cho buổi khám",
                "Ngừng hút thuốc lá ngay lập tức nếu có"
            ]
        elif risk_level == "MEDIUM":
            recommendations = [
                "Tham khảo ý kiến bác sĩ chuyên khoa trong thời gian sớm nhất",
                "Theo dõi các triệu chứng và ghi chép lại",
                "Thực hiện lối sống lành mạnh",
                "Tránh các yếu tố nguy cơ như khói thuốc"
            ]
        else:
            recommendations = [
                "Duy trì khám sức khỏe định kỳ",
                "Tiếp tục lối sống lành mạnh",
                "Theo dõi sức khỏe và báo cáo bất thường",
                "Tránh các yếu tố nguy cơ"
            ]

        return {
            "analysis": analysis_text,
            "structured_analysis": {
                "overview": analysis_text,
                "detailed_analysis": "Phân tích chi tiết cần có sự hỗ trợ của AI chuyên nghiệp.",
                "recommendations": recommendations,
                "next_steps": [
                    "Tham khảo ý kiến bác sĩ chuyên khoa",
                    "Thực hiện các xét nghiệm bổ sung nếu cần"
                ],
                "important_notes": "Đây chỉ là công cụ hỗ trợ, không thay thế chẩn đoán y tế chuyên nghiệp."
            },
            "recommendations": recommendations,
            "risk_assessment": risk_level,
            "confidence": "BASIC_ANALYSIS",
            "timestamp": datetime.now().isoformat()
        }

    def get_health_tips(self, risk_level: str) -> List[str]:
        """Get health tips based on risk level"""

        base_tips = [
            "Không hút thuốc lá và tránh khói thuốc thụ động",
            "Duy trì chế độ ăn uống lành mạnh với nhiều rau xanh",
            "Tập thể dục thường xuyên",
            "Khám sức khỏe định kỳ"
        ]

        if risk_level == "HIGH":
            return [
                "Ngừng hút thuốc ngay lập tức",
                "Tránh tiếp xúc với các chất gây ung thư",
                "Tăng cường dinh dưỡng và nghỉ ngơi",
                "Theo dõi sát sao các triệu chứng"
            ] + base_tips

        elif risk_level == "MEDIUM":
            return [
                "Giảm thiểu các yếu tố nguy cơ",
                "Tăng cường hệ miễn dịch",
                "Quản lý stress hiệu quả"
            ] + base_tips

        return base_tips

# Global instance
gemini_service = GeminiAnalysisService()