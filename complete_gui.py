#!/usr/bin/env python3
"""
Complete GUI for Lung Cancer Detection System
Tích hợp Survey Model, YOLO Detection và Gemini AI Analysis
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import requests
import json
import os
from PIL import Image, ImageTk
import base64
import io
import threading
from datetime import datetime

class LungCancerDetectionGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🏥 Lung Cancer Detection System - Complete Analysis")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')

        # Backend API URL
        self.api_base_url = "http://localhost:8000"

        # Variables
        self.current_image_path = None
        self.current_image_data = None
        self.survey_data = {}

        self.setup_ui()
        self.check_backend_connection()

    def setup_ui(self):
        """Setup the complete user interface"""
        # Main title
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x', pady=(0, 10))
        title_frame.pack_propagate(False)

        title_label = tk.Label(
            title_frame,
            text="🏥 Lung Cancer Detection System",
            font=('Arial', 20, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(expand=True)

        subtitle_label = tk.Label(
            title_frame,
            text="AI-Powered Medical Analysis with Survey & Image Detection",
            font=('Arial', 12),
            fg='#ecf0f1',
            bg='#2c3e50'
        )
        subtitle_label.pack()

        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=5)

        # Create tabs
        self.create_survey_tab()
        self.create_image_tab()
        self.create_combined_tab()
        self.create_results_tab()

        # Status bar
        self.status_frame = tk.Frame(self.root, bg='#34495e', height=30)
        self.status_frame.pack(fill='x', side='bottom')
        self.status_frame.pack_propagate(False)

        self.status_label = tk.Label(
            self.status_frame,
            text="Ready - Checking backend connection...",
            font=('Arial', 10),
            fg='white',
            bg='#34495e'
        )
        self.status_label.pack(side='left', padx=10, pady=5)

    def create_survey_tab(self):
        """Create survey prediction tab"""
        survey_frame = ttk.Frame(self.notebook)
        self.notebook.add(survey_frame, text="📋 Survey Analysis")

        # Main container
        main_container = tk.Frame(survey_frame, bg='white')
        main_container.pack(fill='both', expand=True, padx=10, pady=10)

        # Title
        title = tk.Label(
            main_container,
            text="Medical Survey Analysis",
            font=('Arial', 16, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title.pack(pady=(0, 20))

        # Create scrollable frame for survey questions
        canvas = tk.Canvas(main_container, bg='white')
        scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='white')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Survey questions
        self.survey_vars = {}
        questions = [
            ("Gender", "gender", ["Female", "Male"]),
            ("Age", "age", None),  # Entry field
            ("Smoking", "smoking", ["No", "Yes"]),
            ("Yellow Fingers", "yellow_fingers", ["No", "Yes"]),
            ("Anxiety", "anxiety", ["No", "Yes"]),
            ("Peer Pressure", "peer_pressure", ["No", "Yes"]),
            ("Chronic Disease", "chronic_disease", ["No", "Yes"]),
            ("Fatigue", "fatigue", ["No", "Yes"]),
            ("Allergy", "allergy", ["No", "Yes"]),
            ("Wheezing", "wheezing", ["No", "Yes"]),
            ("Alcohol Consuming", "alcohol_consuming", ["No", "Yes"]),
            ("Coughing", "coughing", ["No", "Yes"]),
            ("Shortness of Breath", "shortness_of_breath", ["No", "Yes"]),
            ("Swallowing Difficulty", "swallowing_difficulty", ["No", "Yes"]),
            ("Chest Pain", "chest_pain", ["No", "Yes"])
        ]

        for i, (label, key, options) in enumerate(questions):
            question_frame = tk.Frame(scrollable_frame, bg='white')
            question_frame.pack(fill='x', pady=5, padx=20)

            # Question label
            q_label = tk.Label(
                question_frame,
                text=f"{i+1}. {label}:",
                font=('Arial', 11, 'bold'),
                bg='white',
                anchor='w'
            )
            q_label.pack(fill='x')

            if key == "age":
                # Age entry
                self.survey_vars[key] = tk.StringVar()
                age_entry = tk.Entry(
                    question_frame,
                    textvariable=self.survey_vars[key],
                    font=('Arial', 10),
                    width=10
                )
                age_entry.pack(anchor='w', pady=(5, 0))
            else:
                # Radio buttons
                self.survey_vars[key] = tk.IntVar()
                radio_frame = tk.Frame(question_frame, bg='white')
                radio_frame.pack(anchor='w', pady=(5, 0))

                for j, option in enumerate(options):
                    radio = tk.Radiobutton(
                        radio_frame,
                        text=option,
                        variable=self.survey_vars[key],
                        value=j,
                        font=('Arial', 10),
                        bg='white'
                    )
                    radio.pack(side='left', padx=(0, 20))

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Buttons
        button_frame = tk.Frame(main_container, bg='white')
        button_frame.pack(fill='x', pady=20)

        analyze_btn = tk.Button(
            button_frame,
            text="🔍 Analyze Survey",
            command=self.analyze_survey,
            font=('Arial', 12, 'bold'),
            bg='#3498db',
            fg='white',
            padx=20,
            pady=10
        )
        analyze_btn.pack(side='left', padx=10)

        clear_btn = tk.Button(
            button_frame,
            text="🗑️ Clear Form",
            command=self.clear_survey,
            font=('Arial', 12),
            bg='#95a5a6',
            fg='white',
            padx=20,
            pady=10
        )
        clear_btn.pack(side='left', padx=10)

        # Results area
        results_frame = tk.LabelFrame(main_container, text="Survey Results", font=('Arial', 12, 'bold'))
        results_frame.pack(fill='both', expand=True, pady=(20, 0))

        self.survey_results_text = scrolledtext.ScrolledText(
            results_frame,
            height=8,
            font=('Arial', 10),
            wrap=tk.WORD
        )
        self.survey_results_text.pack(fill='both', expand=True, padx=10, pady=10)

    def create_image_tab(self):
        """Create image analysis tab"""
        image_frame = ttk.Frame(self.notebook)
        self.notebook.add(image_frame, text="🖼️ Image Analysis")

        # Main container
        main_container = tk.Frame(image_frame, bg='white')
        main_container.pack(fill='both', expand=True, padx=10, pady=10)

        # Title
        title = tk.Label(
            main_container,
            text="CT Scan Image Analysis",
            font=('Arial', 16, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title.pack(pady=(0, 20))

        # Image selection and display
        image_section = tk.Frame(main_container, bg='white')
        image_section.pack(fill='both', expand=True)

        # Left side - Controls
        controls_frame = tk.Frame(image_section, bg='white', width=300)
        controls_frame.pack(side='left', fill='y', padx=(0, 20))
        controls_frame.pack_propagate(False)

        # File selection
        file_frame = tk.LabelFrame(controls_frame, text="Image Selection", font=('Arial', 11, 'bold'))
        file_frame.pack(fill='x', pady=(0, 20))

        select_btn = tk.Button(
            file_frame,
            text="📁 Select CT Scan Image",
            command=self.select_image,
            font=('Arial', 11),
            bg='#3498db',
            fg='white',
            padx=15,
            pady=8
        )
        select_btn.pack(pady=10)

        self.image_path_label = tk.Label(
            file_frame,
            text="No image selected",
            font=('Arial', 9),
            bg='white',
            wraplength=280
        )
        self.image_path_label.pack(pady=(0, 10))

        # Analysis controls
        analysis_frame = tk.LabelFrame(controls_frame, text="Analysis", font=('Arial', 11, 'bold'))
        analysis_frame.pack(fill='x', pady=(0, 20))

        analyze_img_btn = tk.Button(
            analysis_frame,
            text="🔍 Analyze Image",
            command=self.analyze_image,
            font=('Arial', 11, 'bold'),
            bg='#e74c3c',
            fg='white',
            padx=15,
            pady=8,
            state='disabled'
        )
        analyze_img_btn.pack(pady=10)
        self.analyze_img_btn = analyze_img_btn

        clear_img_btn = tk.Button(
            analysis_frame,
            text="🗑️ Clear Image",
            command=self.clear_image,
            font=('Arial', 11),
            bg='#95a5a6',
            fg='white',
            padx=15,
            pady=8
        )
        clear_img_btn.pack(pady=(0, 10))

        # Detection results
        detection_frame = tk.LabelFrame(controls_frame, text="Detection Results", font=('Arial', 11, 'bold'))
        detection_frame.pack(fill='both', expand=True)

        self.detection_results_text = scrolledtext.ScrolledText(
            detection_frame,
            height=10,
            font=('Arial', 9),
            wrap=tk.WORD
        )
        self.detection_results_text.pack(fill='both', expand=True, padx=5, pady=5)

        # Right side - Image display
        image_display_frame = tk.LabelFrame(image_section, text="CT Scan Image", font=('Arial', 11, 'bold'))
        image_display_frame.pack(side='right', fill='both', expand=True)

        # Canvas for image
        self.image_canvas = tk.Canvas(image_display_frame, bg='#f8f9fa', relief='sunken', bd=2)
        self.image_canvas.pack(fill='both', expand=True, padx=10, pady=10)

        # Placeholder text
        self.image_canvas.create_text(
            400, 200,
            text="Select a CT scan image to begin analysis",
            font=('Arial', 14),
            fill='#bdc3c7',
            tags="placeholder"
        )

    def create_combined_tab(self):
        """Create combined analysis tab"""
        combined_frame = ttk.Frame(self.notebook)
        self.notebook.add(combined_frame, text="🔬 Combined Analysis")

        # Main container
        main_container = tk.Frame(combined_frame, bg='white')
        main_container.pack(fill='both', expand=True, padx=10, pady=10)

        # Title
        title = tk.Label(
            main_container,
            text="Complete Medical Analysis",
            font=('Arial', 16, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title.pack(pady=(0, 20))

        # Instructions
        instructions = tk.Label(
            main_container,
            text="This tab combines survey data and image analysis with AI-powered medical insights.\nComplete the survey and upload an image, then run the combined analysis.",
            font=('Arial', 11),
            bg='white',
            fg='#7f8c8d',
            justify='center'
        )
        instructions.pack(pady=(0, 20))

        # Status indicators
        status_frame = tk.Frame(main_container, bg='white')
        status_frame.pack(fill='x', pady=(0, 20))

        # Survey status
        survey_status_frame = tk.Frame(status_frame, bg='white')
        survey_status_frame.pack(side='left', expand=True, fill='x', padx=10)

        tk.Label(survey_status_frame, text="📋 Survey Data:", font=('Arial', 11, 'bold'), bg='white').pack()
        self.survey_status_label = tk.Label(
            survey_status_frame,
            text="❌ Not completed",
            font=('Arial', 10),
            bg='white',
            fg='#e74c3c'
        )
        self.survey_status_label.pack()

        # Image status
        image_status_frame = tk.Frame(status_frame, bg='white')
        image_status_frame.pack(side='right', expand=True, fill='x', padx=10)

        tk.Label(image_status_frame, text="🖼️ Image Data:", font=('Arial', 11, 'bold'), bg='white').pack()
        self.image_status_label = tk.Label(
            image_status_frame,
            text="❌ Not uploaded",
            font=('Arial', 10),
            bg='white',
            fg='#e74c3c'
        )
        self.image_status_label.pack()

        # Combined analysis button
        combined_btn = tk.Button(
            main_container,
            text="🚀 Run Complete Analysis",
            command=self.run_combined_analysis,
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            padx=30,
            pady=15
        )
        combined_btn.pack(pady=20)
        self.combined_btn = combined_btn

        # Results area
        results_frame = tk.LabelFrame(main_container, text="Combined Analysis Results", font=('Arial', 12, 'bold'))
        results_frame.pack(fill='both', expand=True, pady=(20, 0))

        self.combined_results_text = scrolledtext.ScrolledText(
            results_frame,
            height=15,
            font=('Arial', 10),
            wrap=tk.WORD
        )
        self.combined_results_text.pack(fill='both', expand=True, padx=10, pady=10)

    def create_results_tab(self):
        """Create results summary tab"""
        results_frame = ttk.Frame(self.notebook)
        self.notebook.add(results_frame, text="📊 Results Summary")

        # Main container
        main_container = tk.Frame(results_frame, bg='white')
        main_container.pack(fill='both', expand=True, padx=10, pady=10)

        # Title
        title = tk.Label(
            main_container,
            text="Analysis Results Summary",
            font=('Arial', 16, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title.pack(pady=(0, 20))

        # Results summary area
        summary_frame = tk.LabelFrame(main_container, text="Summary", font=('Arial', 12, 'bold'))
        summary_frame.pack(fill='both', expand=True)

        self.summary_text = scrolledtext.ScrolledText(
            summary_frame,
            font=('Arial', 11),
            wrap=tk.WORD
        )
        self.summary_text.pack(fill='both', expand=True, padx=10, pady=10)

        # Export button
        export_btn = tk.Button(
            main_container,
            text="💾 Export Results",
            command=self.export_results,
            font=('Arial', 12),
            bg='#9b59b6',
            fg='white',
            padx=20,
            pady=10
        )
        export_btn.pack(pady=10)

    def check_backend_connection(self):
        """Check if backend is running"""
        def check():
            try:
                response = requests.get(f"{self.api_base_url}/", timeout=5)
                if response.status_code == 200:
                    self.status_label.config(text="✅ Backend connected - Ready for analysis")
                else:
                    self.status_label.config(text="❌ Backend error - Check server status")
            except requests.exceptions.RequestException:
                self.status_label.config(text="❌ Backend not available - Please start the backend server")

        # Run in separate thread to avoid blocking UI
        threading.Thread(target=check, daemon=True).start()

    def analyze_survey(self):
        """Analyze survey data"""
        try:
            # Validate and collect survey data
            survey_data = {}

            # Validate age
            try:
                age = int(self.survey_vars['age'].get())
                if age < 0 or age > 120:
                    raise ValueError("Age must be between 0 and 120")
                survey_data['age'] = age
            except ValueError:
                messagebox.showerror("Error", "Please enter a valid age (0-120)")
                return

            # Collect other survey data
            for key, var in self.survey_vars.items():
                if key != 'age':
                    survey_data[key] = var.get()

            # Update status
            self.status_label.config(text="🔄 Analyzing survey data...")
            self.root.update()

            # Send request to backend
            response = requests.post(
                f"{self.api_base_url}/predict/survey",
                data=survey_data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                self.display_survey_results(result)
                self.survey_data = result  # Store for combined analysis
                self.update_survey_status(True)
                self.status_label.config(text="✅ Survey analysis completed")
            else:
                error_msg = response.json().get('detail', 'Unknown error')
                messagebox.showerror("Error", f"Analysis failed: {error_msg}")
                self.status_label.config(text="❌ Survey analysis failed")

        except requests.exceptions.RequestException as e:
            messagebox.showerror("Connection Error", f"Cannot connect to backend: {str(e)}")
            self.status_label.config(text="❌ Connection error")
        except Exception as e:
            messagebox.showerror("Error", f"Unexpected error: {str(e)}")
            self.status_label.config(text="❌ Analysis error")

    def display_survey_results(self, result):
        """Display survey analysis results"""
        self.survey_results_text.delete(1.0, tk.END)

        # Format results
        text = f"""SURVEY ANALYSIS RESULTS
{'='*50}

PREDICTION: {result['prediction']}
CONFIDENCE:
  • No Cancer: {result['confidence']['no_cancer']:.1%}
  • Cancer Risk: {result['confidence']['cancer']:.1%}

RISK LEVEL: {result['risk_level']}

ANALYSIS:
"""

        if result['prediction'] == 'YES':
            text += f"""⚠️  HIGH RISK DETECTED
The survey indicates a {result['confidence']['cancer']:.1%} probability of lung cancer risk.
This requires immediate medical attention.

RECOMMENDATIONS:
• Contact a pulmonologist immediately
• Prepare medical history for consultation
• Stop smoking if applicable
• Schedule comprehensive lung examination
"""
        else:
            text += f"""✅ LOW RISK INDICATED
The survey shows a {result['confidence']['no_cancer']:.1%} probability of no cancer risk.
However, regular health monitoring is still recommended.

RECOMMENDATIONS:
• Continue regular health check-ups
• Maintain healthy lifestyle
• Monitor any new symptoms
• Avoid risk factors like smoking
"""

        text += f"""
INPUT DATA SUMMARY:
{'='*30}
"""

        # Display input features
        feature_names = {
            'GENDER': 'Gender',
            'AGE': 'Age',
            'SMOKING': 'Smoking',
            'YELLOW_FINGERS': 'Yellow Fingers',
            'ANXIETY': 'Anxiety',
            'PEER_PRESSURE': 'Peer Pressure',
            'CHRONIC DISEASE': 'Chronic Disease',
            'FATIGUE': 'Fatigue',
            'ALLERGY': 'Allergy',
            'WHEEZING': 'Wheezing',
            'ALCOHOL CONSUMING': 'Alcohol Consuming',
            'COUGHING': 'Coughing',
            'SHORTNESS OF BREATH': 'Shortness of Breath',
            'SWALLOWING DIFFICULTY': 'Swallowing Difficulty',
            'CHEST PAIN': 'Chest Pain'
        }

        for feature, value in result['input_features'].items():
            display_name = feature_names.get(feature, feature)
            if feature == 'GENDER':
                display_value = 'Male' if value == 1 else 'Female'
            elif feature == 'AGE':
                display_value = str(int(value))
            else:
                display_value = 'Yes' if value == 1 else 'No'

            text += f"• {display_name}: {display_value}\n"

        text += f"\nAnalysis completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        self.survey_results_text.insert(1.0, text)

    def clear_survey(self):
        """Clear survey form"""
        for var in self.survey_vars.values():
            if isinstance(var, tk.StringVar):
                var.set("")
            else:
                var.set(0)

        self.survey_results_text.delete(1.0, tk.END)
        self.survey_data = {}
        self.update_survey_status(False)
        self.status_label.config(text="Survey form cleared")

    def select_image(self):
        """Select CT scan image"""
        file_types = [
            ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
            ("All files", "*.*")
        ]

        filename = filedialog.askopenfilename(
            title="Select CT Scan Image",
            filetypes=file_types
        )

        if filename:
            self.current_image_path = filename
            self.load_and_display_image(filename)
            self.analyze_img_btn.config(state='normal')
            self.image_path_label.config(text=f"Selected: {os.path.basename(filename)}")
            self.update_image_status(True)

    def load_and_display_image(self, filepath):
        """Load and display image in canvas"""
        try:
            # Load image
            image = Image.open(filepath)

            # Get canvas size
            self.image_canvas.update()
            canvas_width = self.image_canvas.winfo_width()
            canvas_height = self.image_canvas.winfo_height()

            # Resize image to fit canvas
            img_width, img_height = image.size
            scale = min(canvas_width/img_width, canvas_height/img_height, 1.0) * 0.9

            if scale < 1.0:
                new_width = int(img_width * scale)
                new_height = int(img_height * scale)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # Convert to PhotoImage
            self.current_image_data = ImageTk.PhotoImage(image)

            # Clear canvas and display image
            self.image_canvas.delete("all")

            # Center image
            x = (canvas_width - image.width) // 2
            y = (canvas_height - image.height) // 2

            self.image_canvas.create_image(x, y, anchor='nw', image=self.current_image_data)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image: {str(e)}")

    def analyze_image(self):
        """Analyze CT scan image"""
        if not self.current_image_path:
            messagebox.showerror("Error", "No image selected")
            return

        try:
            self.status_label.config(text="🔄 Analyzing CT scan image...")
            self.root.update()

            # Prepare file for upload
            with open(self.current_image_path, 'rb') as f:
                files = {'file': f}

                response = requests.post(
                    f"{self.api_base_url}/predict/image",
                    files=files,
                    timeout=60
                )

            if response.status_code == 200:
                result = response.json()
                self.display_image_results(result)
                self.image_data = result  # Store for combined analysis
                self.status_label.config(text="✅ Image analysis completed")
            else:
                error_msg = response.json().get('detail', 'Unknown error')
                messagebox.showerror("Error", f"Image analysis failed: {error_msg}")
                self.status_label.config(text="❌ Image analysis failed")

        except requests.exceptions.RequestException as e:
            messagebox.showerror("Connection Error", f"Cannot connect to backend: {str(e)}")
            self.status_label.config(text="❌ Connection error")
        except Exception as e:
            messagebox.showerror("Error", f"Unexpected error: {str(e)}")
            self.status_label.config(text="❌ Analysis error")

    def display_image_results(self, result):
        """Display image analysis results"""
        self.detection_results_text.delete(1.0, tk.END)

        text = f"""IMAGE ANALYSIS RESULTS
{'='*40}

TOTAL DETECTIONS: {result['total_detections']}
STATUS: {result['status'].replace('_', ' ').title()}

"""

        if result['detections']:
            text += "DETECTED CANCER TYPES:\n"
            text += "=" * 25 + "\n"

            for i, detection in enumerate(result['detections']):
                confidence = detection['confidence']
                cancer_type = detection['cancer_type']

                # Add risk indicator
                risk_indicator = "🔴 HIGH" if confidence > 0.7 else "🟡 MEDIUM" if confidence > 0.4 else "🟢 LOW"

                text += f"""
{i+1}. {cancer_type}
   Confidence: {confidence:.1%}
   Risk Level: {risk_indicator}
   Location: ({detection['bbox']['x1']}, {detection['bbox']['y1']}) to ({detection['bbox']['x2']}, {detection['bbox']['y2']})
"""

            text += f"""
RECOMMENDATIONS:
• Immediate consultation with oncologist required
• Prepare for additional diagnostic tests
• Bring all medical records to appointment
• Consider second opinion if needed
"""
        else:
            text += """✅ NO CANCER DETECTED

The AI analysis did not detect any obvious signs of lung cancer in this CT scan.

RECOMMENDATIONS:
• Continue regular health monitoring
• Maintain healthy lifestyle
• Schedule routine check-ups as recommended
• Report any new symptoms to your doctor

NOTE: This is an AI screening tool and should not replace professional medical diagnosis.
"""

        text += f"\nAnalysis completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        self.detection_results_text.insert(1.0, text)

        # Display annotated image if available
        if 'annotated_image' in result:
            try:
                # Decode base64 image
                img_data = base64.b64decode(result['annotated_image'])
                image = Image.open(io.BytesIO(img_data))

                # Resize and display
                canvas_width = self.image_canvas.winfo_width()
                canvas_height = self.image_canvas.winfo_height()

                img_width, img_height = image.size
                scale = min(canvas_width/img_width, canvas_height/img_height, 1.0) * 0.9

                if scale < 1.0:
                    new_width = int(img_width * scale)
                    new_height = int(img_height * scale)
                    image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

                self.current_image_data = ImageTk.PhotoImage(image)

                # Clear and display annotated image
                self.image_canvas.delete("all")
                x = (canvas_width - image.width) // 2
                y = (canvas_height - image.height) // 2
                self.image_canvas.create_image(x, y, anchor='nw', image=self.current_image_data)

            except Exception as e:
                print(f"Error displaying annotated image: {e}")

    def clear_image(self):
        """Clear image and results"""
        self.current_image_path = None
        self.current_image_data = None
        self.image_data = {}

        # Clear canvas
        self.image_canvas.delete("all")
        self.image_canvas.create_text(
            400, 200,
            text="Select a CT scan image to begin analysis",
            font=('Arial', 14),
            fill='#bdc3c7',
            tags="placeholder"
        )

        # Clear results
        self.detection_results_text.delete(1.0, tk.END)

        # Update UI
        self.analyze_img_btn.config(state='disabled')
        self.image_path_label.config(text="No image selected")
        self.update_image_status(False)
        self.status_label.config(text="Image cleared")

    def run_combined_analysis(self):
        """Run combined analysis with both survey and image data"""
        # Check if we have survey data
        if not hasattr(self, 'survey_data') or not self.survey_data:
            messagebox.showwarning("Warning", "Please complete the survey analysis first")
            return

        # Check if we have image
        if not self.current_image_path:
            messagebox.showwarning("Warning", "Please select and analyze a CT scan image first")
            return

        try:
            self.status_label.config(text="🔄 Running combined AI analysis...")
            self.root.update()

            # Prepare survey data
            survey_data = {}
            for key, var in self.survey_vars.items():
                if key == 'age':
                    survey_data[key] = int(var.get()) if var.get() else 0
                else:
                    survey_data[key] = var.get()

            # Prepare file for upload
            with open(self.current_image_path, 'rb') as f:
                files = {'file': f}

                response = requests.post(
                    f"{self.api_base_url}/analyze/combined",
                    data=survey_data,
                    files=files,
                    timeout=120
                )

            if response.status_code == 200:
                result = response.json()
                self.display_combined_results(result)
                self.update_results_summary(result)
                self.status_label.config(text="✅ Combined analysis completed")
            else:
                error_msg = response.json().get('detail', 'Unknown error')
                messagebox.showerror("Error", f"Combined analysis failed: {error_msg}")
                self.status_label.config(text="❌ Combined analysis failed")

        except requests.exceptions.RequestException as e:
            messagebox.showerror("Connection Error", f"Cannot connect to backend: {str(e)}")
            self.status_label.config(text="❌ Connection error")
        except Exception as e:
            messagebox.showerror("Error", f"Unexpected error: {str(e)}")
            self.status_label.config(text="❌ Analysis error")

    def display_combined_results(self, result):
        """Display combined analysis results"""
        self.combined_results_text.delete(1.0, tk.END)

        text = f"""COMPLETE MEDICAL ANALYSIS REPORT
{'='*60}

ANALYSIS TIMESTAMP: {result.get('timestamp', 'N/A')}

"""

        # Survey Analysis Section
        if result.get('survey_analysis'):
            survey = result['survey_analysis']
            text += f"""📋 SURVEY ANALYSIS
{'='*30}
Prediction: {survey['prediction']}
Cancer Risk: {survey['confidence']['cancer']:.1%}
Risk Level: {survey['risk_level']}

"""

        # Image Analysis Section
        if result.get('image_analysis'):
            image = result['image_analysis']
            text += f"""🖼️ IMAGE ANALYSIS
{'='*30}
Detections: {image['total_detections']}
Status: {image['status'].replace('_', ' ').title()}

"""
            if image.get('detections'):
                text += "Detected Cancer Types:\n"
                for i, detection in enumerate(image['detections']):
                    text += f"  {i+1}. {detection['cancer_type']}: {detection['confidence']:.1%}\n"
                text += "\n"

        # AI Analysis Section
        if result.get('ai_analysis'):
            ai = result['ai_analysis']
            text += f"""🤖 AI MEDICAL ANALYSIS
{'='*40}
Risk Assessment: {ai['risk_assessment']}
Confidence: {ai['confidence']}

DETAILED ANALYSIS:
{ai.get('analysis', 'No detailed analysis available')}

RECOMMENDATIONS:
"""
            for i, rec in enumerate(ai.get('recommendations', []), 1):
                text += f"{i}. {rec}\n"

            text += "\n"

        # Overall Assessment
        text += f"""📊 OVERALL ASSESSMENT
{'='*35}
"""

        # Determine overall risk
        survey_risk = result.get('survey_analysis', {}).get('risk_level', 'UNKNOWN')
        image_detections = result.get('image_analysis', {}).get('total_detections', 0)
        ai_risk = result.get('ai_analysis', {}).get('risk_assessment', 'UNKNOWN')

        if survey_risk == 'HIGH' or image_detections > 0 or ai_risk == 'HIGH':
            text += """🔴 HIGH RISK DETECTED
URGENT ACTION REQUIRED:
• Contact oncologist immediately
• Schedule comprehensive examination
• Prepare all medical records
• Consider emergency consultation if symptoms worsen

"""
        elif survey_risk == 'MEDIUM' or ai_risk == 'MEDIUM':
            text += """🟡 MODERATE RISK DETECTED
RECOMMENDED ACTIONS:
• Schedule appointment with pulmonologist
• Monitor symptoms closely
• Follow up within 2-4 weeks
• Maintain healthy lifestyle

"""
        else:
            text += """🟢 LOW RISK INDICATED
PREVENTIVE MEASURES:
• Continue regular health check-ups
• Maintain healthy lifestyle
• Monitor for any new symptoms
• Follow standard screening guidelines

"""

        text += f"""
IMPORTANT DISCLAIMER:
This analysis is provided by AI assistance tools and should NOT replace professional medical diagnosis. Always consult with qualified healthcare professionals for proper medical evaluation and treatment decisions.

Report generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        self.combined_results_text.insert(1.0, text)

    def update_survey_status(self, completed):
        """Update survey status indicator"""
        if completed:
            self.survey_status_label.config(text="✅ Completed", fg='#27ae60')
        else:
            self.survey_status_label.config(text="❌ Not completed", fg='#e74c3c')

    def update_image_status(self, uploaded):
        """Update image status indicator"""
        if uploaded:
            self.image_status_label.config(text="✅ Uploaded", fg='#27ae60')
        else:
            self.image_status_label.config(text="❌ Not uploaded", fg='#e74c3c')

    def update_results_summary(self, result):
        """Update results summary tab"""
        self.summary_text.delete(1.0, tk.END)

        # Create comprehensive summary
        summary = f"""LUNG CANCER DETECTION - COMPREHENSIVE REPORT
{'='*70}

PATIENT INFORMATION:
• Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
• Report ID: LCD-{datetime.now().strftime('%Y%m%d-%H%M%S')}

ANALYSIS SUMMARY:
"""

        # Add survey summary
        if result.get('survey_analysis'):
            survey = result['survey_analysis']
            summary += f"""
📋 SURVEY RESULTS:
   • Prediction: {survey['prediction']}
   • Cancer Probability: {survey['confidence']['cancer']:.1%}
   • Risk Level: {survey['risk_level']}
"""

        # Add image summary
        if result.get('image_analysis'):
            image = result['image_analysis']
            summary += f"""
🖼️ IMAGE ANALYSIS:
   • Detections Found: {image['total_detections']}
   • Analysis Status: {image['status'].replace('_', ' ').title()}
"""
            if image.get('detections'):
                summary += "   • Detected Types:\n"
                for detection in image['detections']:
                    summary += f"     - {detection['cancer_type']}: {detection['confidence']:.1%}\n"

        # Add AI analysis summary
        if result.get('ai_analysis'):
            ai = result['ai_analysis']
            summary += f"""
🤖 AI ASSESSMENT:
   • Overall Risk: {ai['risk_assessment']}
   • Analysis Confidence: {ai['confidence']}
   • Key Recommendations: {len(ai.get('recommendations', []))} items
"""

        summary += f"""
NEXT STEPS:
{'='*20}
Based on the comprehensive analysis, the following actions are recommended:
"""

        if result.get('ai_analysis', {}).get('recommendations'):
            for i, rec in enumerate(result['ai_analysis']['recommendations'], 1):
                summary += f"{i}. {rec}\n"

        summary += f"""
TECHNICAL DETAILS:
{'='*25}
• Survey Model: Ensemble ML (99.78% accuracy)
• Image Model: YOLOv11m (Cancer Detection)
• AI Analysis: Gemini Pro (Medical Insights)
• Processing Time: {datetime.now().strftime('%H:%M:%S')}

DISCLAIMER:
This report is generated by AI-assisted medical screening tools and is intended for informational purposes only. It should not be used as a substitute for professional medical advice, diagnosis, or treatment. Always seek the advice of qualified healthcare providers with any questions regarding medical conditions.
"""

        self.summary_text.insert(1.0, summary)

        # Switch to results tab
        self.notebook.select(3)  # Results tab is index 3

    def export_results(self):
        """Export results to file"""
        try:
            # Get current results
            results_content = self.summary_text.get(1.0, tk.END)

            if not results_content.strip():
                messagebox.showwarning("Warning", "No results to export")
                return

            # Ask for save location
            filename = filedialog.asksaveasfilename(
                title="Export Results",
                defaultextension=".txt",
                filetypes=[
                    ("Text files", "*.txt"),
                    ("All files", "*.*")
                ]
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(results_content)

                messagebox.showinfo("Success", f"Results exported to:\n{filename}")
                self.status_label.config(text="✅ Results exported successfully")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to export results: {str(e)}")

def main():
    """Main function"""
    try:
        root = tk.Tk()

        # Center window
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (1200 // 2)
        y = (root.winfo_screenheight() // 2) - (800 // 2)
        root.geometry(f"1200x800+{x}+{y}")

        app = LungCancerDetectionGUI(root)
        root.mainloop()

    except Exception as e:
        print(f"Failed to start GUI: {e}")
        messagebox.showerror("Error", f"Failed to start GUI: {e}")

if __name__ == "__main__":
    main()