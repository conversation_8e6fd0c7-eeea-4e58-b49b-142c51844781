#!/usr/bin/env python3
"""
Script to start the Lung Cancer Detection Backend
"""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """Check if required packages are installed"""
    try:
        import fastapi
        import uvicorn
        import google.generativeai
        import ultralytics
        import cv2
        import sklearn
        print("✅ All required packages are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        print("Please install requirements: pip install -r requirements.txt")
        return False

def check_models():
    """Check if model files exist"""
    models_exist = True

    survey_model = Path("model/new_lung_cancer_model.pkl")
    if not survey_model.exists():
        print(f"❌ Survey model not found: {survey_model}")
        models_exist = False
    else:
        print(f"✅ Survey model found: {survey_model}")

    yolo_model = Path("model/best.pt")
    if not yolo_model.exists():
        print(f"❌ YOLO model not found: {yolo_model}")
        models_exist = False
    else:
        print(f"✅ YOLO model found: {yolo_model}")

    return models_exist

def check_env():
    """Check environment configuration"""
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️ .env file not found. Creating from .env.example...")
        example_file = Path(".env.example")
        if example_file.exists():
            import shutil
            shutil.copy(example_file, env_file)
            print("✅ .env file created. Please edit it with your API keys.")
        else:
            print("❌ .env.example not found")
            return False

    # Load and check environment variables
    from dotenv import load_dotenv
    load_dotenv()

    gemini_key = os.getenv("GEMINI_API_KEY")
    if not gemini_key or gemini_key == "your_gemini_api_key_here":
        print("⚠️ GEMINI_API_KEY not configured. AI analysis will be disabled.")
    else:
        print("✅ GEMINI_API_KEY configured")

    return True

def start_backend():
    """Start the FastAPI backend"""
    print("\n🚀 Starting Lung Cancer Detection Backend...")

    # Change to backend directory
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found")
        return False

    try:
        # Start uvicorn server
        os.chdir(backend_dir)
        subprocess.run([
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n👋 Backend stopped by user")
    except Exception as e:
        print(f"❌ Error starting backend: {e}")
        return False

    return True

def main():
    """Main function"""
    print("🏥 Lung Cancer Detection Backend Startup")
    print("=" * 50)

    # Check requirements
    if not check_requirements():
        return 1

    # Check models
    if not check_models():
        print("\n❌ Model files missing. Please ensure model files are in the 'model' directory.")
        return 1

    # Check environment
    if not check_env():
        return 1

    # Start backend
    print("\n" + "=" * 50)
    print("🌐 Backend will be available at: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("Press Ctrl+C to stop the server")
    print("=" * 50)

    if not start_backend():
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())