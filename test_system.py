#!/usr/bin/env python3
"""
Test script for Lung Cancer Detection System
Tests all components: Backend API, Models, and GUI functionality
"""

import requests
import json
import os
import sys
import time
from pathlib import Path
import subprocess
import threading

class SystemTester:
    def __init__(self):
        self.api_base_url = "http://localhost:8000"
        self.test_results = []
        self.backend_process = None

    def log_test(self, test_name, status, message=""):
        """Log test result"""
        result = {
            "test": test_name,
            "status": status,
            "message": message,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)

        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {status}")
        if message:
            print(f"   {message}")

    def test_requirements(self):
        """Test if all required packages are installed"""
        print("\n🔍 Testing Requirements...")

        required_packages = [
            'fastapi', 'uvicorn', 'requests', 'numpy', 'opencv-python',
            'pillow', 'ultralytics', 'scikit-learn', 'pandas', 'google-generativeai'
        ]

        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                self.log_test(f"Package: {package}", "PASS")
            except ImportError:
                self.log_test(f"Package: {package}", "FAIL", f"Package not installed")

    def test_model_files(self):
        """Test if model files exist"""
        print("\n🔍 Testing Model Files...")

        models = [
            ("Survey Model", "model/new_lung_cancer_model.pkl"),
            ("YOLO Model", "model/best.pt")
        ]

        for name, path in models:
            if Path(path).exists():
                file_size = Path(path).stat().st_size / (1024 * 1024)  # MB
                self.log_test(f"{name}", "PASS", f"Size: {file_size:.1f} MB")
            else:
                self.log_test(f"{name}", "FAIL", f"File not found: {path}")

    def start_backend(self):
        """Start backend server for testing"""
        print("\n🚀 Starting Backend Server...")

        try:
            # Change to backend directory and start server
            backend_dir = Path("backend")
            if not backend_dir.exists():
                self.log_test("Backend Directory", "FAIL", "Backend directory not found")
                return False

            # Start backend in background
            self.backend_process = subprocess.Popen([
                sys.executable, "-m", "uvicorn", "main:app",
                "--host", "0.0.0.0", "--port", "8000"
            ], cwd=backend_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            # Wait for server to start
            for i in range(30):  # Wait up to 30 seconds
                try:
                    response = requests.get(f"{self.api_base_url}/", timeout=2)
                    if response.status_code == 200:
                        self.log_test("Backend Startup", "PASS", "Server started successfully")
                        return True
                except requests.exceptions.RequestException:
                    time.sleep(1)

            self.log_test("Backend Startup", "FAIL", "Server failed to start within 30 seconds")
            return False

        except Exception as e:
            self.log_test("Backend Startup", "FAIL", f"Error: {str(e)}")
            return False

    def test_backend_health(self):
        """Test backend health endpoint"""
        print("\n🔍 Testing Backend Health...")

        try:
            response = requests.get(f"{self.api_base_url}/", timeout=10)

            if response.status_code == 200:
                data = response.json()
                self.log_test("Health Endpoint", "PASS", f"Status: {data.get('status')}")

                # Check model status
                models = data.get('models', {})
                for model_name, loaded in models.items():
                    status = "PASS" if loaded else "FAIL"
                    self.log_test(f"Model: {model_name}", status)
            else:
                self.log_test("Health Endpoint", "FAIL", f"HTTP {response.status_code}")

        except requests.exceptions.RequestException as e:
            self.log_test("Health Endpoint", "FAIL", f"Connection error: {str(e)}")

    def test_survey_prediction(self):
        """Test survey prediction endpoint"""
        print("\n🔍 Testing Survey Prediction...")

        # Test data - low risk case
        test_data = {
            'gender': 0,  # Female
            'age': 25,
            'smoking': 0,  # No
            'yellow_fingers': 0,
            'anxiety': 0,
            'peer_pressure': 0,
            'chronic_disease': 0,
            'fatigue': 0,
            'allergy': 0,
            'wheezing': 0,
            'alcohol_consuming': 0,
            'coughing': 0,
            'shortness_of_breath': 0,
            'swallowing_difficulty': 0,
            'chest_pain': 0
        }

        try:
            response = requests.post(
                f"{self.api_base_url}/predict/survey",
                data=test_data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                prediction = result.get('prediction')
                confidence = result.get('confidence', {})

                self.log_test("Survey Prediction", "PASS",
                             f"Prediction: {prediction}, Cancer Risk: {confidence.get('cancer', 0):.1%}")
            else:
                error_msg = response.json().get('detail', 'Unknown error')
                self.log_test("Survey Prediction", "FAIL", f"HTTP {response.status_code}: {error_msg}")

        except requests.exceptions.RequestException as e:
            self.log_test("Survey Prediction", "FAIL", f"Request error: {str(e)}")
        except Exception as e:
            self.log_test("Survey Prediction", "FAIL", f"Unexpected error: {str(e)}")

    def test_image_prediction(self):
        """Test image prediction endpoint"""
        print("\n🔍 Testing Image Prediction...")

        # Create a dummy test image if none exists
        test_image_path = "test_image.jpg"

        if not Path(test_image_path).exists():
            try:
                from PIL import Image
                import numpy as np

                # Create a simple test image
                img_array = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
                img = Image.fromarray(img_array)
                img.save(test_image_path)
                self.log_test("Test Image Creation", "PASS", f"Created: {test_image_path}")
            except Exception as e:
                self.log_test("Test Image Creation", "FAIL", f"Error: {str(e)}")
                return

        try:
            with open(test_image_path, 'rb') as f:
                files = {'file': f}
                response = requests.post(
                    f"{self.api_base_url}/predict/image",
                    files=files,
                    timeout=60
                )

            if response.status_code == 200:
                result = response.json()
                detections = result.get('total_detections', 0)
                status = result.get('status', 'unknown')

                self.log_test("Image Prediction", "PASS",
                             f"Detections: {detections}, Status: {status}")
            else:
                error_msg = response.json().get('detail', 'Unknown error')
                self.log_test("Image Prediction", "FAIL", f"HTTP {response.status_code}: {error_msg}")

        except requests.exceptions.RequestException as e:
            self.log_test("Image Prediction", "FAIL", f"Request error: {str(e)}")
        except Exception as e:
            self.log_test("Image Prediction", "FAIL", f"Unexpected error: {str(e)}")
        finally:
            # Clean up test image
            if Path(test_image_path).exists():
                Path(test_image_path).unlink()

    def test_combined_analysis(self):
        """Test combined analysis endpoint"""
        print("\n🔍 Testing Combined Analysis...")

        # Test data
        test_data = {
            'gender': 1,  # Male
            'age': 55,
            'smoking': 1,  # Yes - higher risk
            'yellow_fingers': 1,
            'anxiety': 0,
            'peer_pressure': 0,
            'chronic_disease': 0,
            'fatigue': 1,
            'allergy': 0,
            'wheezing': 1,
            'alcohol_consuming': 0,
            'coughing': 1,
            'shortness_of_breath': 1,
            'swallowing_difficulty': 0,
            'chest_pain': 1
        }

        # Create test image
        test_image_path = "test_combined.jpg"
        try:
            from PIL import Image
            import numpy as np

            img_array = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
            img = Image.fromarray(img_array)
            img.save(test_image_path)

            with open(test_image_path, 'rb') as f:
                files = {'file': f}
                response = requests.post(
                    f"{self.api_base_url}/analyze/combined",
                    data=test_data,
                    files=files,
                    timeout=120
                )

            if response.status_code == 200:
                result = response.json()

                # Check components
                has_survey = 'survey_analysis' in result
                has_image = 'image_analysis' in result
                has_ai = 'ai_analysis' in result

                self.log_test("Combined Analysis", "PASS",
                             f"Survey: {has_survey}, Image: {has_image}, AI: {has_ai}")

                if has_ai:
                    ai_analysis = result['ai_analysis']
                    risk = ai_analysis.get('risk_assessment', 'Unknown')
                    self.log_test("AI Analysis", "PASS", f"Risk Assessment: {risk}")

            else:
                error_msg = response.json().get('detail', 'Unknown error')
                self.log_test("Combined Analysis", "FAIL", f"HTTP {response.status_code}: {error_msg}")

        except Exception as e:
            self.log_test("Combined Analysis", "FAIL", f"Error: {str(e)}")
        finally:
            if Path(test_image_path).exists():
                Path(test_image_path).unlink()

    def test_gui_components(self):
        """Test GUI components (basic import test)"""
        print("\n🔍 Testing GUI Components...")

        try:
            # Test if GUI can be imported
            import tkinter as tk
            self.log_test("Tkinter Import", "PASS")

            # Test PIL for image handling
            from PIL import Image, ImageTk
            self.log_test("PIL Import", "PASS")

            # Test if complete_gui can be imported
            sys.path.append('.')
            import complete_gui
            self.log_test("Complete GUI Import", "PASS")

        except ImportError as e:
            self.log_test("GUI Components", "FAIL", f"Import error: {str(e)}")
        except Exception as e:
            self.log_test("GUI Components", "FAIL", f"Error: {str(e)}")

    def stop_backend(self):
        """Stop backend server"""
        if self.backend_process:
            print("\n🛑 Stopping Backend Server...")
            self.backend_process.terminate()
            self.backend_process.wait()
            self.log_test("Backend Shutdown", "PASS")

    def generate_report(self):
        """Generate test report"""
        print("\n" + "="*60)
        print("📊 TEST REPORT SUMMARY")
        print("="*60)

        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])

        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if result['status'] == 'FAIL':
                    print(f"  • {result['test']}: {result['message']}")

        # Save detailed report
        report_file = f"test_report_{time.strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)

        print(f"\n📄 Detailed report saved: {report_file}")

        return failed_tests == 0

    def run_all_tests(self):
        """Run all tests"""
        print("🧪 LUNG CANCER DETECTION SYSTEM - COMPREHENSIVE TESTING")
        print("="*70)

        try:
            # Test requirements
            self.test_requirements()

            # Test model files
            self.test_model_files()

            # Test GUI components
            self.test_gui_components()

            # Start backend and test APIs
            if self.start_backend():
                time.sleep(2)  # Give server time to fully initialize

                self.test_backend_health()
                self.test_survey_prediction()
                self.test_image_prediction()
                self.test_combined_analysis()

        except KeyboardInterrupt:
            print("\n⚠️ Testing interrupted by user")
        except Exception as e:
            print(f"\n❌ Unexpected error during testing: {str(e)}")
        finally:
            self.stop_backend()
            return self.generate_report()

def main():
    """Main function"""
    tester = SystemTester()

    print("Starting comprehensive system test...")
    print("This will test all components of the Lung Cancer Detection System")
    print("Including: Requirements, Models, Backend API, and GUI components")
    print()

    success = tester.run_all_tests()

    if success:
        print("\n🎉 ALL TESTS PASSED! System is ready for use.")
        print("\nTo start the system:")
        print("1. Backend: python start_backend.py")
        print("2. GUI: python complete_gui.py")
        return 0
    else:
        print("\n⚠️ Some tests failed. Please check the issues above.")
        print("Make sure all requirements are installed and models are available.")
        return 1

if __name__ == "__main__":
    sys.exit(main())